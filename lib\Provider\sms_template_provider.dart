import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:amrdev_win_pos/Repository/sales_return_repo.dart';
import 'package:amrdev_win_pos/Repository/sms_template_repo.dart';
import 'package:amrdev_win_pos/model/whatsapp_marketing_sms_template_model.dart';

import '../model/sale_transaction_model.dart';

SmsTemplateRepo smsTemplateRepo = SmsTemplateRepo();
final smsTemplateProvider = FutureProvider.autoDispose<WhatsappMarketingSmsTemplateModel>((ref) => smsTemplateRepo.getAllTemplate());
