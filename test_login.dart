// import 'package:flutter/material.dart';
// import 'package:firebase_core/firebase_core.dart';
// import 'package:firebase_auth/firebase_auth.dart';
// import 'lib/firebase_options.dart';

// /// اختبار تسجيل الدخول على Windows
// void main() async {
//   WidgetsFlutterBinding.ensureInitialized();
  
//   try {
//     // تهيئة Firebase
//     await Firebase.initializeApp(
//       options: DefaultFirebaseOptions.currentPlatform,
//     );
//     print('✅ تم تهيئة Firebase بنجاح');
    
//     // اختبار تسجيل الدخول
//     await testLogin();
    
//   } catch (e) {
//     print('❌ خطأ في الاختبار: $e');
//   }
// }

// /// اختبار تسجيل الدخول
// Future<void> testLogin() async {
//   print('\n🔐 اختبار تسجيل الدخول...');
  
//   // بيانات اختبار (استخدم بيانات صحيحة)
//   const testEmail = '<EMAIL>';
//   const testPassword = 'your_password_here'; // ضع كلمة المرور الصحيحة
  
//   try {
//     print('🔄 محاولة تسجيل الدخول بـ: $testEmail');
    
//     final userCredential = await FirebaseAuth.instance
//         .signInWithEmailAndPassword(
//           email: testEmail, 
//           password: testPassword
//         );
    
//     if (userCredential.user != null) {
//       print('✅ تم تسجيل الدخول بنجاح!');
//       print('📧 الإيميل: ${userCredential.user!.email}');
//       print('🆔 UID: ${userCredential.user!.uid}');
//       print('✅ تم التحقق: ${userCredential.user!.emailVerified}');
      
//       // اختبار الحصول على ID Token
//       final idToken = await userCredential.user!.getIdToken();
//       print('🔑 تم الحصول على ID Token بنجاح');
      
//       // تسجيل الخروج
//       await FirebaseAuth.instance.signOut();
//       print('🚪 تم تسجيل الخروج بنجاح');
      
//     } else {
//       print('❌ فشل في تسجيل الدخول - لا يوجد مستخدم');
//     }
    
//   } on FirebaseAuthException catch (e) {
//     print('❌ خطأ في Firebase Auth: ${e.code}');
//     print('📝 الرسالة: ${e.message}');
    
//     switch (e.code) {
//       case 'user-not-found':
//         print('💡 الحل: تأكد من وجود المستخدم في Firebase Auth');
//         break;
//       case 'wrong-password':
//         print('💡 الحل: تأكد من صحة كلمة المرور');
//         break;
//       case 'invalid-email':
//         print('💡 الحل: تأكد من صحة تنسيق الإيميل');
//         break;
//       case 'user-disabled':
//         print('💡 الحل: تم تعطيل هذا المستخدم');
//         break;
//       default:
//         print('💡 خطأ غير معروف: ${e.code}');
//     }
//   } catch (e) {
//     print('❌ خطأ عام: $e');
//   }
// }
