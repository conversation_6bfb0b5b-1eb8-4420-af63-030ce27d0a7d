import 'dart:convert';

import 'package:firebase_auth/firebase_auth.dart';
import '../services/firebase_database_service.dart';

import '../const.dart';
import '../model/sale_transaction_model.dart';

class SalesReturnRepo {
  Future<List<SaleTransactionModel>> getAllTransition() async {
    List<SaleTransactionModel> transitionList = [];
    try {
      final snapshot = await FirebaseDatabaseService.ref(await getUserID()).child('Sales Return').orderByKey().get();
      for (var element in snapshot.children) {
        transitionList.add(SaleTransactionModel.fromJson(jsonDecode(jsonEncode(element.value))));
      }
    } catch (e) {
      print('❌ خطأ في جلب بيانات إرجاع المبيعات: $e');
    }
    return transitionList;
  }
}
