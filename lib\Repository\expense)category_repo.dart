import 'dart:convert';

import 'package:firebase_auth/firebase_auth.dart';
import '../services/firebase_database_service.dart';
import 'package:salespro_admin/model/expense_category_model.dart';
import 'package:salespro_admin/model/income_catehory_model.dart';

import '../const.dart';

class ExpenseCategoryRepo {
  Future<List<ExpenseCategoryModel>> getAllExpenseCategory() async {
    List<ExpenseCategoryModel> allExpenseCategoryList = [];

    try {
      final snapshot = await FirebaseDatabaseService.ref(await getUserID()).child('Expense Category').orderByKey().get();
      for (var element in snapshot.children) {
        var data = ExpenseCategoryModel.fromJson(jsonDecode(jsonEncode(element.value)));
        allExpenseCategoryList.add(data);
      }
    } catch (e) {
      print('❌ خطأ في جلب فئات المصروفات: $e');
    }
    return allExpenseCategoryList;
  }
}

class IncomeCategoryRepo {
  Future<List<IncomeCategoryModel>> getAllIncomeCategory() async {
    List<IncomeCategoryModel> allIncomeCategoryList = [];

    try {
      final snapshot = await FirebaseDatabaseService.ref(await getUserID()).child('Income Category').orderByKey().get();
      for (var element in snapshot.children) {
        var data = IncomeCategoryModel.fromJson(jsonDecode(jsonEncode(element.value)));
        allIncomeCategoryList.add(data);
      }
    } catch (e) {
      print('❌ خطأ في جلب فئات الإيرادات: $e');
    }
    return allIncomeCategoryList;
  }
}
