#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تعريب النصوص الثابتة في تطبيق Flutter
يقوم بالبحث عن النصوص الإنجليزية الثابتة واستبدالها بالعربية
"""

import os
import re
import json
from pathlib import Path
from typing import Dict, List, Tuple

class ArabicLocalizationTool:
    def __init__(self, project_path: str):
        self.project_path = Path(project_path)
        self.dart_files = []
        self.changes_made = []

        # قاموس الترجمات الشائعة
        self.translations = {
            # أزرار وعناصر واجهة المستخدم
            'Loading...': 'جاري التحميل...',
            'Save': 'حفظ',
            'Cancel': 'إلغاء',
            'Delete': 'حذف',
            'Edit': 'تعديل',
            'Add': 'إضافة',
            'Update': 'تحديث',
            'Search': 'بحث',
            'Filter': 'تصفية',
            'Sort': 'ترتيب',
            'Print': 'طباعة',
            'Export': 'تصدير',
            'Import': 'استيراد',
            'Submit': 'إرسال',
            'Confirm': 'تأكيد',
            'Yes': 'نعم',
            'No': 'لا',
            'OK': 'موافق',
            'Close': 'إغلاق',
            'Back': 'رجوع',
            'Next': 'التالي',
            'Previous': 'السابق',
            'Continue': 'متابعة',
            'Finish': 'إنهاء',
            'Done': 'تم',
            'Complete': 'مكتمل',
            'Pending': 'معلق',
            'Active': 'نشط',
            'Inactive': 'غير نشط',
            'Enable': 'تفعيل',
            'Disable': 'إلغاء تفعيل',
            'Show': 'عرض',
            'Hide': 'إخفاء',
            'View': 'عرض',
            'Details': 'التفاصيل',
            'Settings': 'الإعدادات',
            'Options': 'خيارات',
            'Preferences': 'التفضيلات',
            'Configuration': 'التكوين',
            'Profile': 'الملف الشخصي',
            'Account': 'الحساب',
            'Login': 'تسجيل الدخول',
            'Logout': 'تسجيل الخروج',
            'Register': 'تسجيل',
            'Sign Up': 'إنشاء حساب',
            'Sign In': 'تسجيل الدخول',
            'Forgot Password': 'نسيت كلمة المرور',
            'Reset Password': 'إعادة تعيين كلمة المرور',
            'Change Password': 'تغيير كلمة المرور',
            'Username': 'اسم المستخدم',
            'Password': 'كلمة المرور',
            'Email': 'البريد الإلكتروني',
            'Phone': 'الهاتف',
            'Address': 'العنوان',
            'Name': 'الاسم',
            'First Name': 'الاسم الأول',
            'Last Name': 'الاسم الأخير',
            'Full Name': 'الاسم الكامل',
            'Date': 'التاريخ',
            'Time': 'الوقت',
            'Today': 'اليوم',
            'Yesterday': 'أمس',
            'Tomorrow': 'غداً',
            'This Week': 'هذا الأسبوع',
            'This Month': 'هذا الشهر',
            'This Year': 'هذا العام',

            # مصطلحات نقاط البيع
            'Sales': 'المبيعات',
            'Purchase': 'المشتريات',
            'Inventory': 'المخزون',
            'Stock': 'المخزون',
            'Product': 'المنتج',
            'Products': 'المنتجات',
            'Category': 'الفئة',
            'Categories': 'الفئات',
            'Brand': 'العلامة التجارية',
            'Brands': 'العلامات التجارية',
            'Unit': 'الوحدة',
            'Units': 'الوحدات',
            'Price': 'السعر',
            'Cost': 'التكلفة',
            'Total': 'الإجمالي',
            'Subtotal': 'المجموع الفرعي',
            'Tax': 'الضريبة',
            'Discount': 'الخصم',
            'Amount': 'المبلغ',
            'Quantity': 'الكمية',
            'Qty': 'الكمية',
            'Invoice': 'الفاتورة',
            'Receipt': 'الإيصال',
            'Bill': 'الفاتورة',
            'Payment': 'الدفع',
            'Cash': 'نقدي',
            'Credit': 'ائتمان',
            'Debit': 'خصم',
            'Balance': 'الرصيد',
            'Due': 'المستحق',
            'Paid': 'مدفوع',
            'Unpaid': 'غير مدفوع',
            'Partial': 'جزئي',
            'Customer': 'العميل',
            'Customers': 'العملاء',
            'Supplier': 'المورد',
            'Suppliers': 'الموردين',
            'Vendor': 'البائع',
            'Vendors': 'البائعين',
            'Sale': 'بيع',
            'Purchase': 'شراء',
            'Return': 'إرجاع',
            'Returns': 'المرتجعات',
            'Refund': 'استرداد',
            'Exchange': 'تبديل',
            'Order': 'طلب',
            'Orders': 'الطلبات',
            'Quotation': 'عرض سعر',
            'Quotations': 'عروض الأسعار',
            'Estimate': 'تقدير',
            'Estimates': 'التقديرات',
            'Report': 'تقرير',
            'Reports': 'التقارير',
            'Dashboard': 'لوحة التحكم',
            'Analytics': 'التحليلات',
            'Statistics': 'الإحصائيات',
            'Summary': 'الملخص',
            'Overview': 'نظرة عامة',

            # حالات وأوضاع
            'New': 'جديد',
            'Draft': 'مسودة',
            'Sent': 'مرسل',
            'Delivered': 'تم التسليم',
            'Cancelled': 'ملغي',
            'Approved': 'موافق عليه',
            'Rejected': 'مرفوض',
            'Processing': 'قيد المعالجة',
            'Completed': 'مكتمل',
            'Failed': 'فشل',
            'Success': 'نجح',
            'Error': 'خطأ',
            'Warning': 'تحذير',
            'Info': 'معلومات',
            'Notice': 'إشعار',
            'Alert': 'تنبيه',

            # رسائل شائعة
            'Please wait...': 'يرجى الانتظار...',
            'Processing...': 'جاري المعالجة...',
            'Saving...': 'جاري الحفظ...',
            'Deleting...': 'جاري الحذف...',
            'Updating...': 'جاري التحديث...',
            'Loading data...': 'جاري تحميل البيانات...',
            'No data found': 'لا توجد بيانات',
            'No results': 'لا توجد نتائج',
            'Empty list': 'قائمة فارغة',
            'Select an option': 'اختر خياراً',
            'Choose file': 'اختر ملف',
            'Upload file': 'رفع ملف',
            'Download': 'تحميل',
            'File uploaded successfully': 'تم رفع الملف بنجاح',
            'Operation completed successfully': 'تمت العملية بنجاح',
            'Changes saved successfully': 'تم حفظ التغييرات بنجاح',
            'Item deleted successfully': 'تم حذف العنصر بنجاح',
            'Invalid input': 'إدخال غير صحيح',
            'Required field': 'حقل مطلوب',
            'Field is required': 'هذا الحقل مطلوب',
            'Please enter a valid email': 'يرجى إدخال بريد إلكتروني صحيح',
            'Please enter a valid phone number': 'يرجى إدخال رقم هاتف صحيح',
            'Password too short': 'كلمة المرور قصيرة جداً',
            'Passwords do not match': 'كلمات المرور غير متطابقة',
            'Connection failed': 'فشل الاتصال',
            'Network error': 'خطأ في الشبكة',
            'Server error': 'خطأ في الخادم',
            'Access denied': 'تم رفض الوصول',
            'Permission denied': 'تم رفض الإذن',
            'Unauthorized': 'غير مخول',
            'Session expired': 'انتهت صلاحية الجلسة',
            'Please login again': 'يرجى تسجيل الدخول مرة أخرى',

            # أيام الأسبوع
            'Monday': 'الاثنين',
            'Tuesday': 'الثلاثاء',
            'Wednesday': 'الأربعاء',
            'Thursday': 'الخميس',
            'Friday': 'الجمعة',
            'Saturday': 'السبت',
            'Sunday': 'الأحد',

            # الشهور
            'January': 'يناير',
            'February': 'فبراير',
            'March': 'مارس',
            'April': 'أبريل',
            'May': 'مايو',
            'June': 'يونيو',
            'July': 'يوليو',
            'August': 'أغسطس',
            'September': 'سبتمبر',
            'October': 'أكتوبر',
            'November': 'نوفمبر',
            'December': 'ديسمبر',
        }

    def find_dart_files(self):
        """البحث عن جميع ملفات Dart في المشروع"""
        print("🔍 البحث عن ملفات Dart...")

        for file_path in self.project_path.rglob("*.dart"):
            # تجاهل ملفات البناء والملفات المولدة
            if any(part in str(file_path) for part in ['build', '.dart_tool', 'generated']):
                continue
            self.dart_files.append(file_path)

        print(f"✅ تم العثور على {len(self.dart_files)} ملف Dart")

    def process_file(self, file_path: Path) -> bool:
        """معالجة ملف واحد وتعريب النصوص فيه"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            original_content = content
            changes_in_file = []

            # البحث عن النصوص الثابتة وتعريبها
            for english_text, arabic_text in self.translations.items():
                # البحث عن النص داخل علامات التنصيص
                patterns = [
                    f"'{english_text}'",
                    f'"{english_text}"',
                    f"Text('{english_text}')",
                    f'Text("{english_text}")',
                    f"title: '{english_text}'",
                    f'title: "{english_text}"',
                    f"label: '{english_text}'",
                    f'label: "{english_text}"',
                    f"hint: '{english_text}'",
                    f'hint: "{english_text}"',
                    f"hintText: '{english_text}'",
                    f'hintText: "{english_text}"',
                    f"labelText: '{english_text}'",
                    f'labelText: "{english_text}"',
                ]

                for pattern in patterns:
                    if pattern in content:
                        # استبدال النص الإنجليزي بالعربي
                        new_pattern = pattern.replace(english_text, arabic_text)
                        content = content.replace(pattern, new_pattern)
                        changes_in_file.append(f"  {pattern} → {new_pattern}")

            # إذا تم إجراء تغييرات، احفظ الملف
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)

                self.changes_made.append({
                    'file': str(file_path.relative_to(self.project_path)),
                    'changes': changes_in_file
                })
                return True

        except Exception as e:
            print(f"❌ خطأ في معالجة الملف {file_path}: {e}")

        return False

    def run(self):
        """تشغيل عملية التعريب"""
        print("🚀 بدء عملية تعريب النصوص الثابتة...")

        self.find_dart_files()

        processed_files = 0
        modified_files = 0

        for file_path in self.dart_files:
            processed_files += 1
            if self.process_file(file_path):
                modified_files += 1

            # عرض التقدم
            if processed_files % 10 == 0:
                print(f"📊 تم معالجة {processed_files}/{len(self.dart_files)} ملف...")

        print(f"\n✅ انتهت عملية التعريب!")
        print(f"📁 تم معالجة {processed_files} ملف")
        print(f"✏️ تم تعديل {modified_files} ملف")

        # عرض ملخص التغييرات
        if self.changes_made:
            print(f"\n📋 ملخص التغييرات:")
            for change in self.changes_made[:10]:  # عرض أول 10 ملفات فقط
                print(f"\n📄 {change['file']}:")
                for modification in change['changes'][:5]:  # عرض أول 5 تغييرات فقط
                    print(modification)
                if len(change['changes']) > 5:
                    print(f"  ... و {len(change['changes']) - 5} تغييرات أخرى")

            if len(self.changes_made) > 10:
                print(f"\n... و {len(self.changes_made) - 10} ملفات أخرى تم تعديلها")

        # حفظ تقرير مفصل
        self.save_report()

    def save_report(self):
        """حفظ تقرير مفصل بالتغييرات"""
        report_path = self.project_path / "arabic_localization_report.json"

        report = {
            "timestamp": "2024-01-16",
            "total_files_processed": len(self.dart_files),
            "files_modified": len(self.changes_made),
            "changes": self.changes_made
        }

        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        print(f"\n📊 تم حفظ التقرير المفصل في: {report_path}")

if __name__ == "__main__":
    # تشغيل السكريبت
    project_path = "."  # المجلد الحالي
    tool = ArabicLocalizationTool(project_path)
    tool.run()