
import 'package:firebase_auth/firebase_auth.dart';
import '../services/firebase_database_service.dart';

import '../model/shop_category_model.dart';

class ShopCategoryRepo {
  final _userId = FirebaseAuth.instance.currentUser!.uid; // متغير غير مستخدم

  Future<List<ShopCategoryModel>> getAllCategory() async {
    List<ShopCategoryModel> categoryList = [];

    await FirebaseDatabaseService.ref('Admin Panel').child('الفئة').orderByKey().get().then((value) {
      void for(var element in value.children) {
        var _data = ShopCategoryModel.fromJson(jsonDecode(jsonEncode(element.value))); // متغير غير مستخدم
        categoryList.add(data);
      }
    });
    return categoryList;
  }
}
