import 'dart:convert';

import 'package:firebase_auth/firebase_auth.dart';
import '../services/firebase_database_service.dart';
import 'package:salespro_admin/model/expense_model.dart';

import '../const.dart';

class ExpenseRepo {
  Future<List<ExpenseModel>> getAllExpense() async {
    List<ExpenseModel> allExpense = [];

    try {
      final snapshot = await FirebaseDatabaseService.ref(await getUserID()).child('Expense').orderByKey().get();
      for (var element in snapshot.children) {
        var data = ExpenseModel.fromJson(jsonDecode(jsonEncode(element.value)));
        allExpense.add(data);
      }
    } catch (e) {
      print('❌ خطأ في جلب بيانات المصروفات: $e');
    }
    return allExpense;
  }
}
