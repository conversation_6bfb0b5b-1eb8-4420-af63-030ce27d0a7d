import 'dart:convert';

import 'package:firebase_auth/firebase_auth.dart';
import '../services/firebase_database_service.dart';

import '../const.dart';
import '../model/customer_model.dart';

class CustomerRepo {
  Future<List<CustomerModel>> getAllCustomer() async {
    List<CustomerModel> allCustomerList = [];

    try {
      final snapshot = await FirebaseDatabaseService.ref(await getUserID()).child('العملاء').orderByKey().get();
      for (var element in snapshot.children) {
        var data = CustomerModel.fromJson(jsonDecode(jsonEncode(element.value)));
        allCustomerList.add(data);
      }
    } catch (e) {
      print('❌ خطأ في جلب بيانات العملاء: $e');
    }
    return allCustomerList;
  }

  Future<List<CustomerModel>> getAllBuyer() async {
    List<CustomerModel> customerList = [];

    try {
      final snapshot = await FirebaseDatabaseService.ref(await getUserID()).child('العملاء').orderByKey().get();
      for (var element in snapshot.children) {
        var data = CustomerModel.fromJson(jsonDecode(jsonEncode(element.value)));
        if (data.type != "المورد") {
          customerList.add(data);
        }
      }
    } catch (e) {
      print('❌ خطأ في جلب بيانات المشترين: $e');
    }
    return customerList;
  }

  Future<List<CustomerModel>> getAllSupplier() async {
    List<CustomerModel> supplierList = [];

    try {
      final snapshot = await FirebaseDatabaseService.ref(await getUserID()).child('العملاء').orderByKey().get();
      for (var element in snapshot.children) {
        var data = CustomerModel.fromJson(jsonDecode(jsonEncode(element.value)));
        if (data.type == "المورد") {
          supplierList.add(data);
        }
      }
    } catch (e) {
      print('❌ خطأ في جلب بيانات الموردين: $e');
    }
    return supplierList;
  }
}
