import 'dart:convert';

import 'package:firebase_auth/firebase_auth.dart';
import '../services/firebase_database_service.dart';
import 'package:amrdev_win_pos/model/purchase_transation_model.dart';

import '../const.dart';
import '../model/sale_transaction_model.dart';

class PurchaseReturnRepo {
  Future<List<PurchaseTransactionModel>> getAllTransition() async {
    List<PurchaseTransactionModel> transitionList = [];
    try {
      final snapshot = await FirebaseDatabaseService.ref(await getUserID()).child('Purchase Return').orderByKey().get();
      for (var element in snapshot.children) {
        transitionList.add(PurchaseTransactionModel.fromJson(jsonDecode(jsonEncode(element.value))));
      }
    } catch (e) {
      print('❌ خطأ في جلب بيانات إرجاع المشتريات: $e');
    }
    return transitionList;
  }
}
