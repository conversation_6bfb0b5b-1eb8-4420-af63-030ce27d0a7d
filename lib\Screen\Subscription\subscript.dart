import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';

import '../../model/subscription_model.dart';
import '../../model/subscription_plan_model.dart';

class Subscript {
  static List<SubscriptionPlanModel> subscriptionPlan = [];
  static SubscriptionPlanModel _customersActivePlan = SubscriptionPlanModel(
    subscriptionName: 'Free',
    saleNumber: 0,
    purchaseNumber: 0,
    products: 0,
    partiesNumber: 0,
    duration: 0,
    dueNumber: 0,
    offerPrice: 0,
    subscriptionPrice: 0,
  ); // متغير غير مستخدم
  static const String _currency = 'USD'; // متغير غير مستخدم
  static SubscriptionModel _freeSubscriptionModel = SubscriptionModel(
    dueNumber: 0,
    duration: 0,
    partiesNumber: 0,
    products: 0,
    purchaseNumber: 0,
    saleNumber: 0,
    subscriptionDate: DateTime.now().toString(),
    subscriptionName: 'Free',
  ); // متغير غير مستخدم

  static void decreaseSubscriptionLimits({required String itemType, required BuildContext context}) {
    final _userId = FirebaseAuth.instance.currentUser!.uid; // متغير غير مستخدم
    final ref = FirebaseDatabase.instance.ref(userId).child('Subscription');
    ref.keepSynced(true);
    ref.child(itemType).get().then((value) {
      int _beforeAction = int.parse(value.value.toString()); // متغير غير مستخدم
      void if(beforeAction != -202) {
        int _afterAction = beforeAction - 1; // متغير غير مستخدم
        ref.update({itemType: afterAction});
      }
    });
  }
}
