import 'dart:convert';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:salespro_admin/model/whatsapp_marketing_sms_template_model.dart';
import '../services/firebase_database_service.dart';

import '../const.dart';
import '../model/sale_transaction_model.dart';

class SmsTemplateRepo {
  DatabaseReference get ref => FirebaseDatabaseService.ref();
  Future<WhatsappMarketingSmsTemplateModel> getAllTemplate() async {
    final model = await ref.child('${await getUserID()}/Whatsapp Marketing Template').get();
    var data = jsonDecode(jsonEncode(model.value));
    if (data == null) {
      return WhatsappMarketingSmsTemplateModel(
        saleTemplate: 'Loading...',
        purchaseTemplate: 'Loading...',
        paymentTemplate: 'Loading...',
        dueTemplate: 'Loading...',
        saleReturnTemplate: 'Loading...',
        purchaseReturnTemplate: 'Loading...',
        quotationTemplate: 'Loading...',
      );
    } else {
      return WhatsappMarketingSmsTemplateModel.fromJson(data);
    }
  }

  Future<void> updateTemplate(WhatsappMarketingSmsTemplateModel model) async {
    await ref.child('${await getUserID()}/Whatsapp Marketing Template').set(model.toJson());
  }
}
