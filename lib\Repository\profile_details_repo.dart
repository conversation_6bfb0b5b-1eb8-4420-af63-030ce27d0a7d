import 'dart:convert';
import 'package:flutter/foundation.dart';

import 'package:firebase_auth/firebase_auth.dart';

import '../const.dart';
import '../currency.dart';
import '../model/personal_information_model.dart';
import '../services/firebase_database_service.dart';

class ProfileRepo {
  DatabaseReference get ref => FirebaseDatabaseService.ref();

  Future<PersonalInformationModel> getDetails() async {
    PersonalInformationModel personalInfo = PersonalInformationModel(
      companyName: 'جاري التحميل...',
      businessCategory: 'جاري التحميل...',
      countryName: 'جاري التحميل...',
      language: 'جاري التحميل...',
      phoneNumber: 'جاري التحميل...',
      pictureUrl: 'https://cdn.pixabay.com/photo/2017/06/13/12/53/profile-2398782_960_720.png',
      shopOpeningBalance: 0,
      dueInvoiceCounter: 1,
      purchaseInvoiceCounter: 1,
      saleInvoiceCounter: 1,
      remainingShopBalance: 0,
      currency: '\$',
      currentLocale: 'en',
      gst: '',
    );
    final model = await ref.child('${await getUserID()}/Personal Information').get();
    var data = jsonDecode(jsonEncode(model.value));
    if (data == null) {
      currency = personalInfo.currency;
      return personalInfo;
    } else {
      return PersonalInformationModel.fromJson(data);
    }
  }
  Future<bool> isProfileSetupDone() async {
    try {
      debugPrint('🔍 فحص إعداد الملف الشخصي...');
      final userId = await getUserID();
      debugPrint('🆔 معرف المستخدم: $userId');

      final model = await ref.child('$userId/Personal Information').get();
      debugPrint('📊 نتيجة الاستعلام: exists=${model.exists}, value=${model.value}');

      if (model.exists && model.value != null) {
        debugPrint('✅ تم العثور على معلومات شخصية - الملف الشخصي معد');
        return true;
      } else {
        debugPrint('ℹ️ لم يتم العثور على معلومات شخصية - الملف الشخصي غير معد');
        return false;
      }
    } catch (e) {
      debugPrint('❌ خطأ في فحص إعداد الملف الشخصي: $e');
      return false;
    }
  }
}
